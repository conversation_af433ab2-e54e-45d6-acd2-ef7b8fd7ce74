'use client'

import Link from 'next/link'
import { useSession, signOut } from 'next-auth/react'
import { useState } from 'react'
import { Menu, X, User, LogOut, Settings } from 'lucide-react'

export function Header() {
  const { data: session } = useSession()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="bg-black text-white shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="text-2xl font-bold text-red-500">
            MODELO VIP
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="hover:text-red-400 transition-colors">
              Home
            </Link>
            <Link href="/anuncios" className="hover:text-red-400 transition-colors">
              <PERSON><PERSON><PERSON><PERSON>
            </Link>
            <Link href="/cidades" className="hover:text-red-400 transition-colors">
              Cidades
            </Link>
            
            {session ? (
              <div className="flex items-center space-x-4">
                <span className="text-sm">Olá, {session.user.name}</span>
                {session.user.role === 'ADMIN' && (
                  <Link href="/admin" className="hover:text-red-400 transition-colors">
                    <Settings className="w-5 h-5" />
                  </Link>
                )}
                <button
                  onClick={() => signOut()}
                  className="hover:text-red-400 transition-colors"
                >
                  <LogOut className="w-5 h-5" />
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link href="/auth/login" className="hover:text-red-400 transition-colors">
                  Login
                </Link>
                <Link 
                  href="/auth/register" 
                  className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded transition-colors"
                >
                  Cadastrar
                </Link>
              </div>
            )}
          </nav>

          {/* Mobile menu button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-700">
            <nav className="flex flex-col space-y-4">
              <Link href="/" className="hover:text-red-400 transition-colors">
                Home
              </Link>
              <Link href="/anuncios" className="hover:text-red-400 transition-colors">
                Anúncios
              </Link>
              <Link href="/cidades" className="hover:text-red-400 transition-colors">
                Cidades
              </Link>
              
              {session ? (
                <>
                  <span className="text-sm text-gray-300">Olá, {session.user.name}</span>
                  {session.user.role === 'ADMIN' && (
                    <Link href="/admin" className="hover:text-red-400 transition-colors">
                      Painel Admin
                    </Link>
                  )}
                  <button
                    onClick={() => signOut()}
                    className="text-left hover:text-red-400 transition-colors"
                  >
                    Sair
                  </button>
                </>
              ) : (
                <>
                  <Link href="/auth/login" className="hover:text-red-400 transition-colors">
                    Login
                  </Link>
                  <Link href="/auth/register" className="hover:text-red-400 transition-colors">
                    Cadastrar
                  </Link>
                </>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
