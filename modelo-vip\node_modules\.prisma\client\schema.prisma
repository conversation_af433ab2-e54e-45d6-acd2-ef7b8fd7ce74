// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// Modelo de usuários do sistema
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos
  profiles Profile[]

  @@map("users")
}

// Modelo de perfis de acompanhantes
model Profile {
  id          String   @id @default(cuid())
  name        String
  age         Int
  city        String
  state       String
  phone       String
  email       String
  description String?
  services    String?
  price       String?
  available   Boolean  @default(true)
  verified    <PERSON><PERSON><PERSON>  @default(false)
  featured    <PERSON><PERSON><PERSON>  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relacionamentos
  userId String
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  photos Photo[]

  @@map("profiles")
}

// Modelo de fotos dos perfis
model Photo {
  id        String   @id @default(cuid())
  url       String
  filename  String
  isPrimary Boolean  @default(false)
  createdAt DateTime @default(now())

  // Relacionamentos
  profileId String
  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@map("photos")
}

// Modelo de configurações do site
model SiteConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  updatedAt   DateTime @updatedAt

  @@map("site_config")
}

// Enum para roles de usuário
enum Role {
  USER
  ADMIN
  MODERATOR
}
