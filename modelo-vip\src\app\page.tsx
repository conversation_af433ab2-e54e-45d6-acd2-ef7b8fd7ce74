import Link from 'next/link'
import { Heart, Shield, Star, MapPin } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-red-600 to-red-800 text-white">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative container mx-auto px-4 py-20">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Modelo VIP - Acompanhantes de Luxo nas principais capitais do Brasil
              </h1>
              <p className="text-xl mb-8 leading-relaxed">
                Seja bem-vindo ao Modelo VIP, o portal líder em acompanhantes de luxo no Brasil. Nossa missão é conectar
                você com as mais belas e sofisticadas acompanhantes disponíveis em todo o país, oferecendo uma experiência
                única e inesquecível.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/anuncios"
                  className="bg-white text-red-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-center"
                >
                  Ver Anúncios
                </Link>
                <Link
                  href="/auth/register"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-red-600 transition-colors text-center"
                >
                  Anunciar
                </Link>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8">
                <h3 className="text-2xl font-bold mb-4">Por Que Escolher o Modelo VIP Acompanhantes?</h3>
                <p className="text-lg leading-relaxed">
                  No Modelo VIP, você encontra uma seleção exclusiva de acompanhantes de luxo que atendem aos mais altos
                  padrões de qualidade e discrição. Nossa plataforma é cuidadosamente monitorada para garantir que você tenha
                  uma experiência premium, seja para um jantar romântico ou para um encontro mais íntimo.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              Encontre a Acompanhante de Luxo Perfeita no Modelo Vip
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Com o Modelo VIP, encontrar a acompanhante ideal é simples e seguro. Explore nosso catálogo e descubra
              acompanhantes que se destacam pela beleza e pelo profissionalismo.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-white rounded-lg shadow-lg">
              <Shield className="w-16 h-16 text-red-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold mb-4">Segurança e Discrição</h3>
              <p className="text-gray-600">
                Todas as informações e negociações são mantidas em total sigilo, proporcionando a
                segurança que você precisa.
              </p>
            </div>

            <div className="text-center p-8 bg-white rounded-lg shadow-lg">
              <Star className="w-16 h-16 text-red-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold mb-4">Qualidade Premium</h3>
              <p className="text-gray-600">
                Perfis detalhados com fotos e serviços oferecidos, facilitando sua escolha.
              </p>
            </div>

            <div className="text-center p-8 bg-white rounded-lg shadow-lg">
              <MapPin className="w-16 h-16 text-red-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold mb-4">Cobertura Nacional</h3>
              <p className="text-gray-600">
                Atendimento em todas as principais capitais do Brasil com acompanhantes verificadas.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How it Works Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">Como Funciona o Modelo VIP Acompanhantes?</h2>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Perfis Detalhados</h3>
                    <p className="text-gray-600">
                      Cada acompanhante possui um perfil completo com descrições, fotos e serviços oferecidos,
                      facilitando sua escolha.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Contato Direto</h3>
                    <p className="text-gray-600">
                      Entre em contato diretamente com a acompanhante de sua escolha para discutir detalhes do
                      encontro e fazer sua reserva.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Privacidade Garantida</h3>
                    <p className="text-gray-600">
                      Suas informações e negociações são mantidas em total sigilo, proporcionando a
                      segurança que você precisa.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-100 rounded-lg p-8">
              <h3 className="text-2xl font-bold mb-6 text-center">Nossos Diferenciais</h3>
              <ul className="space-y-4">
                <li className="flex items-center space-x-3">
                  <Heart className="w-5 h-5 text-red-600" />
                  <span>Variedade de Perfis: Oferecemos uma ampla variedade de perfis</span>
                </li>
                <li className="flex items-center space-x-3">
                  <Heart className="w-5 h-5 text-red-600" />
                  <span>Atendimento Personalizado: Nossas acompanhantes são treinadas</span>
                </li>
                <li className="flex items-center space-x-3">
                  <Heart className="w-5 h-5 text-red-600" />
                  <span>Facilidade de Navegação: Nosso site foi projetado</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-red-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">Pronto para uma Experiência Única?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Explore nosso catálogo de acompanhantes de luxo e encontre a companhia perfeita para qualquer ocasião.
          </p>
          <Link
            href="/anuncios"
            className="bg-white text-red-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors inline-block"
          >
            Explorar Anúncios
          </Link>
        </div>
      </section>
    </div>
  )
}
