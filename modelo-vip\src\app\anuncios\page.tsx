'use client'

import { useState, useEffect } from 'react'
import { MapPin, Phone, Mail, Star, Heart } from 'lucide-react'
import Link from 'next/link'

interface Profile {
  id: string
  name: string
  age: number
  city: string
  state: string
  phone: string
  email: string
  description: string | null
  services: string | null
  price: string | null
  available: boolean
  verified: boolean
  featured: boolean
  photos: { id: string; url: string; isPrimary: boolean }[]
}

export default function AnunciosPage() {
  const [profiles, setProfiles] = useState<Profile[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCity, setSelectedCity] = useState('')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchProfiles()
  }, [])

  const fetchProfiles = async () => {
    try {
      const response = await fetch('/api/profiles')
      if (response.ok) {
        const data = await response.json()
        setProfiles(data)
      }
    } catch (error) {
      console.error('Erro ao carregar perfis:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredProfiles = profiles.filter(profile => {
    const matchesCity = !selectedCity || profile.city.toLowerCase().includes(selectedCity.toLowerCase())
    const matchesSearch = !searchTerm || 
      profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.city.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesCity && matchesSearch && profile.available
  })

  const cities = [...new Set(profiles.map(p => p.city))].sort()

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando anúncios...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-black text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-center mb-4">Acompanhantes Curitiba</h1>
          <p className="text-center text-gray-300 mb-8">
            ATENÇÃO, anúncios para Curitiba - PR são cobrados.
          </p>
          <p className="text-center text-sm text-gray-400">
            Não se preocupe, os dados abaixo só serão vistos pela equipe do Modelo VIP.
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Buscar por nome ou cidade..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              />
            </div>
            <div className="md:w-64">
              <select
                value={selectedCity}
                onChange={(e) => setSelectedCity(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">Todas as cidades</option>
                {cities.map(city => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>
            <Link
              href="/anuncios/criar"
              className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors text-center"
            >
              Criar Anúncio
            </Link>
          </div>
        </div>
      </div>

      {/* Profiles Grid */}
      <div className="container mx-auto px-4 py-8">
        {filteredProfiles.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg">Nenhum anúncio encontrado.</p>
            <Link
              href="/anuncios/criar"
              className="inline-block mt-4 bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
            >
              Seja o primeiro a anunciar
            </Link>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProfiles.map((profile) => (
              <div key={profile.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                {/* Profile Image */}
                <div className="relative h-64 bg-gray-200">
                  {profile.photos.length > 0 ? (
                    <img
                      src={profile.photos.find(p => p.isPrimary)?.url || profile.photos[0].url}
                      alt={profile.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <span>Sem foto</span>
                    </div>
                  )}
                  
                  {/* Badges */}
                  <div className="absolute top-2 left-2 flex gap-2">
                    {profile.verified && (
                      <span className="bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold">
                        Verificado
                      </span>
                    )}
                    {profile.featured && (
                      <span className="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                        Destaque
                      </span>
                    )}
                  </div>

                  {/* Favorite Button */}
                  <button className="absolute top-2 right-2 p-2 bg-white/80 rounded-full hover:bg-white transition-colors">
                    <Heart className="w-5 h-5 text-gray-600" />
                  </button>
                </div>

                {/* Profile Info */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-xl font-bold text-gray-800">{profile.name}</h3>
                    <span className="text-lg font-semibold text-red-600">{profile.age} anos</span>
                  </div>

                  <div className="flex items-center text-gray-600 mb-3">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{profile.city}, {profile.state}</span>
                  </div>

                  {profile.description && (
                    <p className="text-gray-600 text-sm mb-4 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {profile.description}
                    </p>
                  )}

                  {profile.price && (
                    <div className="mb-4">
                      <span className="text-lg font-bold text-green-600">{profile.price}</span>
                    </div>
                  )}

                  {/* Contact Buttons */}
                  <div className="flex gap-2">
                    <a
                      href={`tel:${profile.phone}`}
                      className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center"
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Ligar
                    </a>
                    <a
                      href={`mailto:${profile.email}`}
                      className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Email
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
